
/*** Spinner Start ***/
/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}
/*** Spinner End ***/

.back-to-top {
    position: fixed;
    right: 30px;
    bottom: 30px;
    transition: 0.5s;
    z-index: 99;
}

/*** Button Start ***/
.btn {
    font-weight: 600;
    transition: .5s;
}

.btn-square {
    width: 32px;
    height: 32px;
}

.btn-sm-square {
    width: 34px;
    height: 34px;
}

.btn-md-square {
    width: 44px;
    height: 44px;
}

.btn-lg-square {
    width: 56px;
    height: 56px;
}

.btn-xl-square {
    width: 66px;
    height: 66px;
}

.btn-square,
.btn-sm-square,
.btn-md-square,
.btn-lg-square,
.btn-xl-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}

.btn.btn-primary {
    color: var(--bs-white);
}

.btn.btn-primary:hover {
    background: var(--bs-secondary);
    border: 1px solid var(--bs-secondary);
}

.btn.btn-secondary {
    color: var(--bs-white);
   
}

.btn.btn-secondary:hover {
    background: var(--bs-primary);
    border: 1px solid var(--bs-primary);
}
/*** Topbar Start ***/

.dot {
    color: rgb(4, 152, 56); /* Change to your desired color */
}
.elementure {
    color: rgb(6, 0, 0); /* Set Elementure text to white */
}
.smart {
    color: white; /* Set Elementure text to white */
    font-size: 15px;
    position: relative;
    left: 50px;
}
/*** Navbar ***/
.navbar-light .navbar-nav .nav-link {
    position: relative;
    margin-right: 25px;
    padding: 35px 0;
    color: var(--bs-white);
    font-size: 17px;
    font-weight: 400;
    outline: none;
    transition: .5s;
}

.sticky-top.navbar-light .navbar-nav .nav-link {
    padding: 20px 0;
    color: var(--bs-dark);
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link.active {
    color: var(--bs-primary);
}

.navbar-light .navbar-brand img {
    max-height: 60px;
    transition: .5s;
}

.sticky-top.navbar-light .navbar-brand img {
    max-height: 45px;
}

.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    vertical-align: middle;
    margin-left: 8px;
}

.dropdown .dropdown-menu a:hover {
    background: var(--bs-primary);
    color: var(--bs-white);
}

.navbar .nav-item:hover .dropdown-menu {
    transform: rotateX(0deg);
    visibility: visible;
    background: var(--bs-light);
    transition: .5s;
    opacity: 1;
}

@media (max-width: 991.98px) {
    .sticky-top.navbar-light {
        position: relative;
        background: var(--bs-white);
    }

    .navbar.navbar-expand-lg .navbar-toggler {
        padding: 10px 20px;
        border: 1px solid var(--bs-primary);
        color: var(--bs-primary);
    }

    .navbar-light .navbar-collapse {
        margin-top: 15px;
        border-top: 1px solid rgba(0, 0, 0, .08);
    }

    .navbar-light .navbar-nav .nav-link,
    .sticky-top.navbar-light .navbar-nav .nav-link {
        padding: 10px 0;
        margin-left: 0;
        color: var(--bs-dark);
    }

    .navbar-light .navbar-brand img {
        max-height: 45px;
    }
}

@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
        border: 0;
        border-radius: 10px;
        transition: .5s;
        opacity: 0;
    }

    .navbar-light {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        border-top: 0;
        border-right: 0;
        border-bottom: 1px solid;
        border-left: 0;
        border-style: dotted;
        z-index: 999;
    }
    
    .sticky-top.navbar-light {
        position: fixed;
        background: var(--bs-light);
        border: none;
    }

    .navbar-light .navbar-nav .nav-item::before {
        position: absolute;
        content: "";
        width: 0;
        height: 2px;
        bottom: -1px;
        left: 50%;
        background: var(--bs-primary);
        transition: .5s;
    }

    .sticky-top.navbar-light .navbar-nav .nav-item::before {
        bottom: 0;
    }

    .navbar-light .navbar-nav .nav-item::after {
        content: "";
        position: absolute;
        bottom: 1px;
        left: 50%;
        transform: translateX(-50%);
        border: 10px solid;
        border-color: transparent transparent var(--bs-primary) transparent;
        opacity: 0;
        transition: 0.5s;
    }

    .navbar-light .navbar-nav .nav-item:hover::after,
    .navbar-light .navbar-nav .nav-item.active::after {
        bottom: 1px;
        opacity: 1;
    }

    .navbar-light .navbar-nav .nav-item:hover::before,
    .navbar-light .navbar-nav .nav-item.active::before {
        width: calc(100% - 2px);
        left: 1px;
    }

    .navbar-light .navbar-nav .nav-link.nav-contact::before {
        display: none;
    }
}
/*** Carousel Hero Header Start ***/
.carousel-header {
    position: relative;
    overflow: hidden;
}

.carousel-indicators {
    display: none;
}

.carousel .carousel-control-prev,
.carousel .carousel-control-next {
    width: 0;
}

.carousel .carousel-control-prev .carousel-control-prev-icon,
.carousel .carousel-control-next .carousel-control-next-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    padding: 25px 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bs-primary);
    transition: 0.5s;
}

.carousel .carousel-control-prev .carousel-control-prev-icon {
    left: 0;
    border-left: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 50px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 50px;

}

.carousel .carousel-control-next .carousel-control-next-icon {
    right: 0;
    border-right: 0;
    border-top-left-radius: 50px;
    border-top-right-radius: 0;
    border-bottom-left-radius: 50px;
    border-bottom-right-radius: 0;
}

.carousel .carousel-control-prev .carousel-control-prev-icon:hover,
.carousel .carousel-control-next .carousel-control-next-icon:hover {
    background: var(--bs-secondary);
}

.carousel .carousel-inner .carousel-item {
    position: relative;
}

.carousel .carousel-inner .carousel-item .carousel-caption-1  {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding-top: 100px;
    display: flex;
    align-items: center;
    justify-content: end;
    background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, 0.5));
    background-size: cover;
}

.carousel-caption-1-content {
    text-align: end;
    padding-right: 100px;
}

.carousel-caption-1-content .carousel-caption-1-content-btn {
    display: flex;
    justify-content: end;
}

.carousel .carousel-inner .carousel-item .carousel-caption-2  {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding-top: 100px;
    display: flex;
    align-items: center;
    text-align: center;
    background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, 0.5));
    background-size: cover;
}

.carousel-caption-2-content {
    text-align: start;
    padding-left: 100px;
}

.carousel-caption-2-content .carousel-caption-2-content-btn {
    display: flex;
    justify-content: start;
}
.carousel-inner .carousel-item img {
    width: 100%;
    height: 900px; /* Adjust the height you want */
    object-fit: cover; /* Ensures image is cropped, not distorted */
}

@media (max-width: 768px) {
    .carousel-inner .carousel-item img {
        height: 300px;
    }
}

@media (max-width: 992px) {
    .carousel .carousel-inner .carousel-item {
        height: 700px;
        margin-top: -100px;
    }

    .carousel .carousel-inner .carousel-item img {
        height: 700px;
        object-fit: cover;
    }


    .carousel .carousel-inner .carousel-item .carousel-caption-1 {
        max-width: 100% !important;
        justify-content: center;
    }

    .carousel-caption-1-content {
        padding: 0 20px !important;
        text-align: center !important;
    }

    .carousel-caption-1-content .carousel-caption-1-content-btn {
        justify-content: center;
    }


    .carousel .carousel-inner .carousel-item .carousel-caption-2 {
        max-width: 100% !important;
        justify-content: center;
    }

    .carousel-caption-2-content {
        padding: 0 20px !important;
        text-align: center !important;
    }

    .carousel-caption-2-content .carousel-caption-2-content-btn {
        justify-content: center;
    }



    .carousel .carousel-control-prev .carousel-control-prev-icon,
    .carousel .carousel-control-next .carousel-control-next-icon {
        display: none;
    }
}

.modal .modal-header {
    height: 100px;
}

#searchModal .modal-content {
    background: rgba(255, 255, 255, .6);
}
/*** Carousel Hero Header End ***/

/*** Single Page Hero Header Start ***/
.bg-breadcrumb {
    position: relative;
    overflow: hidden;
    background: linear-gradient(rgba(0, 20, 66, 0.7), rgba(0, 20, 66, 0.7)), url(../img/about1.jpg);
    background-position: center top;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 120px 0 60px 0;
    transition: 0.5s;
}

.bg-breadcrumb .breadcrumb {
    position: relative;
}

.bg-breadcrumb .breadcrumb .breadcrumb-item a {
    color: var(--bs-white);
}

@media (max-width: 992px) {
    .bg-breadcrumb {
        padding: 60px 0 60px 0;
    }
}
/*** Single Page Hero Header End ***/


/*** Features Start ***/
.feature .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background: var(--bs-white);
    border-radius: 10px;
    transition: 0.5s;
}

.feature .feature-item:hover {
    box-shadow: 0 0 45px rgba(0, 0, 0, .08);
}

.feature .feature-item .feature-icon {
    width: 100px;
    height: 100px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bs-primary);
}

.feature .feature-item .feature-icon i {
    transition: 0.5s;
}

.feature .feature-item:hover .feature-icon i {
    transform: rotate(360deg);
}

.feature .feature-item a.btn {
    transition: 0.5s;
}

.feature .feature-item:hover a.btn:hover {
    color: var(--bs-primary) !important;
}
/*** Features End ***/

/*** About Start ***/
.about .about-img {
    position: relative;
    padding-bottom: 30px;
    margin-top: 30px; 
    margin-left: 30px;
}

.about .about-img::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 10px;
    top: -30px;
    right: 0;
    border-radius: 10px;
    background: var(--bs-secondary);

}

.about .about-img::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 100%;
    left: -30px;
    bottom: 30px;
    border-radius: 10px;
    background: var(--bs-secondary);
}

.about .about-img .about-exp {
    position: absolute;
    top: 0;
    left: 0;
    padding: 25px;
    font-size: 30px;
    font-weight: bold;
    background: var(--bs-secondary);
    color: var(--bs-white);
    border: 1px solid var(--bs-secondary);
    margin-top: -30px;
    margin-left: -30px;
    border-radius: 10px;
}
/*** About End ***/

/*** Service Start ***/
.service-item {
    position: relative;
    box-shadow: 0 0 45px rgba(0, 0, 0, .08);
}

.service-item::after {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    bottom: 0;
    border-radius: 10px;
    background: var(--bs-primary);
    transition: 0.5s;
    z-index: 1;
}

.service-item:hover::after {
    width: 100%;
    height: 100%;
}

.service-item:hover .service-content,
.service-item:hover .service-btn {
    position: relative;
    z-index: 2;
}

.service-item .service-content a,
.service-item .service-content p {
    transition: 0.5s;
}

.service-item:hover .service-content a:hover {
    color: var(--bs-secondary);
}

.service-item:hover .service-content p {
    color: var(--bs-white);
} 

.service-item .service-btn {
    width: 80px;
    height: 80px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bs-primary);
    transition: 0.5s;
}

.service-item .service-btn i {
    transition: 0.5s;
}

.service-item:hover .service-btn {
    background: var(--bs-white);
}

.service-item:hover .service-btn i {
    transform: rotate(360deg);
    color: var(--bs-primary) !important;
}
/*** Service End ***/


/*** Counter Start ***/
.counter {
    position: relative;
    overflow: hidden;
    background: linear-gradient(rgba(25, 64, 154, 0.9), rgba(25, 64, 154, 1)), url(../img/fact-bg.jpg);
    background-position: center top;
    background-repeat: no-repeat;
    background-size: cover;
}

.counter .counter-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.counter .counter-item .counter-item-icon {
    width: 100px;
    height: 100px;
    border-radius: 50px;
    background: var(--bs-primary);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
/*** Counter End ***/


/*** Products Start ***/
.product .product-item {
    border: 1px solid rgba(196, 211, 211, 0.9);
    border-radius: 10px;
}





/*** Products End ***/

/*** Blog Start ***/
.blog .blog-item {
    border-radius: 10px;
    background: var(--bs-light);
    transition: 0.5s;
}

.blog .blog-item:hover {
    box-shadow: 0 0 45px rgba(0, 0, 0, 0.2);
}

.blog .blog-item .blog-img {
    position: relative;
    overflow: hidden;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.blog .blog-item .blog-img::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    transition: 0.5s;
    z-index: 1;
}

.blog .blog-item .blog-img img {
    transition: 0.5s;
}

.blog .blog-item:hover .blog-img img {
    transform: scale(1.2);
}

.blog .blog-item:hover .blog-img::after {
    background: rgba(0, 209, 249, 0.2);
}

.blog .blog-item .blog-date {
    position: absolute;
    top: 25px;
    left: 25px;
    border-radius: 10px;
    background: var(--bs-primary);
    color: var(--bs-white);
    z-index: 2;
}

.blog .blog-item .blog-content a {
    transition: 0.5s;
}

.blog .blog-item:hover .blog-content a:hover {
    color: var(--bs-primary) !important;
}
/*** Blog End ***/

/*** Team Start ***/
.team .team-item {
    position: relative;
    overflow: hidden;
    background: var(--bs-white);
    border: 1px solid var(--bs-secondary);
    border-radius: 10px;
}

.team .team-item .team-inner {
    transition: 0.5s;
}
.team .team-item:hover .team-inner {
    box-shadow: 0 0 45px rgba(0, 0, 0, 0.2);
}

.team .team-item .team-inner .team-img {
    position: relative;
    z-index: 2;
}

.team .team-item .team-inner .team-img::after {
    content: "";
    width: 100%;
    height: 0;
    position: absolute;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: rgba(0, 209, 249, 0.2);
    transition: 0.5s;
    z-index: 3;
}

.team .team-item:hover .team-inner .team-img::after {
    height: 100%;
}

.team .team-item .team-inner .team-img .team-share {
    position: absolute;
    left: 10px;
    bottom: 10px;
    transition: 0.5s;
    z-index: 2;
}

.team .team-item:hover .team-inner .team-img .team-share {
    opacity: 0;
}

.team .team-item .team-inner .team-img .team-icon {
    position: absolute;
    bottom: 0;
    left: -100%;
    border-bottom-left-radius: 0 !important; 
    border-top-left-radius: 0 !important;
    display: inline-flex;
    background: var(--bs-primary);
    transition: 0.5s;
    z-index: 4;
}

.team .team-item:hover .team-inner .team-img .team-icon { 
    left: -25px; 
}
/*** Team End ***/


/*** testimonial Start ***/
.testimonial .owl-nav .owl-prev {
    position: absolute;
    top: -58px;
    right: 0;
    background: var(--bs-primary);
    color: var(--bs-white);
    padding: 5px 30px;
    border-radius: 30px;
    transition: 0.5s;
}

.testimonial .owl-nav .owl-prev:hover {
    background: var(--bs-secondary);
    color: var(--bs-white);
}

.testimonial .owl-nav .owl-next {
    position: absolute;
    top: -58px;
    right: 88px;
    background: var(--bs-primary);
    color: var(--bs-white);
    padding: 5px 30px;
    border-radius: 30px;
    transition: 0.5s;
}

.testimonial .owl-nav .owl-next:hover {
    background: var(--bs-secondary);
    color: var(--bs-white);
}

.testimonial-carousel .owl-dots {
    display: flex;
    justify-content: center;
    margin-left: 20px;
    margin-top: 20px;
}

.testimonial-carousel .owl-dots .owl-dot {
    width: 20px;
    height: 20px;
    border-radius: 20px;
    margin-right: 15px;
    border: 1px solid var(--bs-secondary);
    background: var(--bs-primary);
    transition: 0.5s;
}

.testimonial-carousel .owl-dots .owl-dot.active {
    width: 20px;
    height: 20px;
    border-radius: 20px;
    border: 1px solid var(--bs-primary);
    background: var(--bs-secondary) !important;
    transition: 0.5s;
}

.testimonial-carousel .owl-dots .owl-dot span {
    position: relative;
    margin-top: 50%;
    margin-left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.testimonial-carousel .owl-dots .owl-dot span::after {
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 8px;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--bs-white);
}

@media (max-width: 992px) {
    .owl-carousel.testimonial-carousel {
        padding-top: 30px;
    }

    .testimonial .owl-nav .owl-prev,
    .testimonial .owl-nav .owl-next {
        top: -30px;
    }
}
/*** testimonial End ***/

/*** Footer Start ***/
.footer {
    background: var(--bs-dark);
}

.footer .footer-item a {
    line-height: 35px;
    color: var(--bs-body);
    transition: 0.5s;
}

.footer .footer-item p {
    line-height: 35px;
}

.footer .footer-item a:hover {
    letter-spacing: 1px;
    color: var(--bs-primary);
}

.footer .footer-item .footer-btn a,
.footer .footer-item .footer-btn a i {
    transition: 0.5s;
}

.footer .footer-item .footer-btn a:hover {
    background: var(--bs-white);
}

.footer .footer-item .footer-btn a:hover i {
    color: var(--bs-primary) !important;
}
.footer .d-flex {
    justify-content: center !important; /* Center horizontally */
}

/*** Footer End ***/

/*** copyright Start ***/
.copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    background: var(--bs-dark);
}
/*** copyright end ***/


