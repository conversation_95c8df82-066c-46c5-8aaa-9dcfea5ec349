version: '3.8'

services:
  website:
    build:
      context: .
      dockerfile: Dockerfile
    image: website-ele:latest
    container_name: website-ele
    ports:
      - "8080:80"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.website.rule=Host(`localhost`)"
      - "traefik.http.services.website.loadbalancer.server.port=80"
